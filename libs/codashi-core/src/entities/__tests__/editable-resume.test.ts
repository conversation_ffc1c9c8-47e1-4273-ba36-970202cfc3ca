import { describe, expect, it } from 'vitest';

import { yamlToEditableResume } from '../resume.yaml';

describe('editableResume schema', () => {
  it('should successfully parse valid YAML to EditableResume object', () => {
    const validYaml = `
      header:
        - name: "name"
          value: "<PERSON>"
        - name: "title"
          value: "Software Engineer"
        - name: "email"
          value: "<EMAIL>"
      sections:
        - name: "work"
          items:
            - name: "Tech Corp"
              position: "Senior Developer"
              start_date: "2020-01-01"
              end_date: "2023-01-01"
              highlights:
                - "Led team of 5 developers"
                - "Implemented CI/CD pipeline"
        - name: "skills"
          items:
            - name: "JavaScript"
              level: "Expert"
              keywords:
                - "React"
                - "Node.js"
                - "TypeScript"
    `;

    const result = yamlToEditableResume(validYaml);

    if (!result.success) {
      console.log('Validation errors:', result.errors);
    }

    expect(result).toEqual({
      success: true,
      data: expect.objectContaining({
        header: expect.arrayContaining([
          expect.objectContaining({
            name: 'name',
            value: '<PERSON>',
          }),
          expect.objectContaining({
            name: 'title',
            value: 'Software Engineer',
          }),
          expect.objectContaining({
            name: 'email',
            value: '<EMAIL>',
          }),
        ]),
        sections: expect.arrayContaining([
          expect.objectContaining({
            name: 'work',
            items: expect.arrayContaining([
              expect.objectContaining({
                name: 'Tech Corp',
                position: 'Senior Developer',
                start_date: '2020-01-01',
                end_date: '2023-01-01',
                highlights: expect.arrayContaining([
                  'Led team of 5 developers',
                  'Implemented CI/CD pipeline',
                ]),
              }),
            ]),
          }),
          expect.objectContaining({
            name: 'skills',
            items: expect.arrayContaining([
              expect.objectContaining({
                name: 'JavaScript',
                level: 'Expert',
                keywords: expect.arrayContaining([
                  'React',
                  'Node.js',
                  'TypeScript',
                ]),
              }),
            ]),
          }),
        ]),
      }),
    });
  });
});
