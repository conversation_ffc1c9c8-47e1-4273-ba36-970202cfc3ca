import { describe, expect, it } from 'vitest';

import { yamlToEditableResume } from '../resume.yaml';

describe('editableResume schema', () => {
  it('should successfully parse valid YAML to EditableResume object', () => {
    const validYaml = `
      header:
        - "<PERSON>"
        - "Software Engineer"
        - "<EMAIL>"
      sections:
        - - name: "Tech Corp"
            position: "Senior Developer"
            start_date: "2020-01-01"
            end_date: "2023-01-01"
            highlights:
              - "Led team of 5 developers"
              - "Implemented CI/CD pipeline"
        - - name: "JavaScript"
            level: "Expert"
            keywords:
              - "React"
              - "Node.js"
              - "TypeScript"
    `;

    const result = yamlToEditableResume(validYaml);

    if (!result.success) {
      console.log('Validation errors:', result.errors);
    }

    expect(result).toEqual({
      success: true,
      data: expect.objectContaining({
        header: expect.arrayContaining([
          '<PERSON>',
          'Software Engineer',
          '<EMAIL>',
        ]),
        sections: expect.arrayContaining([
          expect.arrayContaining([
            expect.objectContaining({
              name: 'Tech Corp',
              position: 'Senior Developer',
              start_date: '2020-01-01',
              end_date: '2023-01-01',
              highlights: expect.arrayContaining([
                'Led team of 5 developers',
                'Implemented CI/CD pipeline',
              ]),
            }),
          ]),
          expect.arrayContaining([
            expect.objectContaining({
              name: 'JavaScript',
              level: 'Expert',
              keywords: expect.arrayContaining([
                'React',
                'Node.js',
                'TypeScript',
              ]),
            }),
          ]),
        ]),
      }),
    });
  });
});
