import { z } from 'zod';

import { Deep<PERSON>artial, isoDate<PERSON><PERSON><PERSON>, zodToDomainOrThrow } from '@awe/core';

// Custom schema for Markdown content with optional template variables
// Supports standard Markdown plus template variables like {{ varName }}
const markdownString = z
  .string()
  .describe(
    'Markdown content with optional template variables using {{ varName }} syntax'
  );

// Basics schema - kept separate as it's used in other parts of the codebase
export const basicsSchema = z.strictObject({
  // Full name
  name: z.string().nullable().default(null),
  // Professional title or role (e.g., "Software Engineer")
  title: z.string().nullable().default(null),
  // URL to a profile image or avatar
  image: z.string().nullable().default(null),
  // Email address
  email: z.string().email().nullable().default(null),
  // Phone number
  phone: z.string().nullable().default(null),
  // Personal website or blog URL
  url: z.string().url().nullable().default(null),
  // Brief summary or bio - supports Markdown with template variables
  summary: markdownString.nullable().default(null),
  // Location information
  location: z
    .strictObject({
      address: z.string().nullable(),
      postal_code: z.string().nullable(),
      city: z.string().nullable(),
      country_code: z.string().nullable(),
      region: z.string().nullable(),
    })
    .nullable()
    .default(null),
  // Array of profiles (e.g., social media, online presence)
  profiles: z
    .array(
      z.strictObject({
        // Network or platform name (e.g., "LinkedIn", "GitHub", "Twitter")
        network: z.string().nullable(),
        // Username or handle on the network
        username: z.string().nullable(),
        // URL to the profile
        url: z.string().url().nullable(),
      })
    )
    .nullable()
    .default(null),
});

// Reference schema - kept separate as it's extended in other parts of the codebase
export const referenceSchema = z.strictObject({
  name: z.string().nullable(),
  reference: z.string().nullable(),
});

// Work experience schema - kept separate as it's used in multiple places
const workSchema = z.strictObject({
  // Company or organization name
  name: z.string().nullable().default(null),
  location: z.string().nullable().default(null),
  // Company description or industry information
  description: markdownString.nullable().default(null),
  // Job title or role
  position: z.string().nullable().default(null),
  url: z.string().url().nullable().default(null),
  start_date: isoDateParser.nullable().default(null),
  // Null/undefined indicates current position
  end_date: isoDateParser.nullable().default(null),
  // Job summary - supports Markdown with template variables
  summary: markdownString.nullable().default(null),
  // Key accomplishments as bullet points
  highlights: z.array(z.string()).nullable().default(null),
});

// Define the main resume schema - follows the JSON Resume standard format
// See: https://jsonresume.org/schema/
const jsonResume = z.strictObject({
  // Reference to the JSON Schema that validates this document
  $schema: z.string().url().nullable().default(null),
  basics: basicsSchema.nullable().default(null),
  work: z.array(workSchema).nullable().default(null),
  volunteer: z
    .array(
      z.strictObject({
        organization: z.string().nullable(),
        position: z.string().nullable(),
        url: z.string().url().nullable(),
        start_date: isoDateParser.nullable(),
        end_date: isoDateParser.nullable(),
        summary: z.string().nullable(),
        highlights: z.array(z.string()).nullable(),
      })
    )
    .nullable()
    .default(null),
  education: z
    .array(
      z.strictObject({
        institution: z.string().nullable(),
        url: z.string().url().nullable(),
        area: z.string().nullable(),
        study_type: z.string().nullable(),
        start_date: isoDateParser.nullable(),
        end_date: isoDateParser.nullable(),
        score: z.string().nullable(),
        courses: z.array(z.string()).nullable(),
      })
    )
    .nullable()
    .default(null),
  awards: z
    .array(
      z.strictObject({
        title: z.string().nullable(),
        date: isoDateParser.nullable(),
        awarder: z.string().nullable(),
        summary: z.string().nullable(),
      })
    )
    .nullable()
    .default(null),
  certificates: z
    .array(
      z.strictObject({
        name: z.string().nullable(),
        date: isoDateParser.nullable(),
        url: z.string().url().nullable(),
        issuer: z.string().nullable(),
      })
    )
    .nullable()
    .default(null),
  publications: z
    .array(
      z.strictObject({
        name: z.string().nullable(),
        publisher: z.string().nullable(),
        release_date: isoDateParser.nullable(),
        url: z.string().url().nullable(),
        summary: z.string().nullable(),
      })
    )
    .nullable()
    .default(null),
  skills: z
    .array(
      z.strictObject({
        // Primary skill name (e.g., "JavaScript", "Project Management")
        name: z.string().nullable(),
        // Proficiency level (e.g., "Beginner", "Intermediate", "Expert")
        level: z.string().nullable(),
        // Related sub-skills, libraries, or specific technologies
        // For example, for "JavaScript" might include ["React", "Node.js", "TypeScript"]
        keywords: z.array(z.string()).nullable(),
      })
    )
    .nullable()
    .default(null),
  languages: z
    .array(
      z.strictObject({
        language: z.string().nullable(),
        fluency: z.string().nullable(),
      })
    )
    .nullable()
    .default(null),
  interests: z
    .array(
      z.strictObject({
        name: z.string().nullable(),
        keywords: z.array(z.string()).nullable(),
      })
    )
    .nullable()
    .default(null),
  references: z.array(referenceSchema).nullable().default(null),
  projects: z
    .array(
      z.strictObject({
        name: z.string().nullable(),
        // Project description - supports Markdown with template variables
        description: markdownString.nullable(),
        // Specific accomplishments or contributions to the project
        highlights: z.array(z.string()).nullable(),
        // Technical skills, tools, and technologies used
        // These can be mapped to the skills taxonomy for matching
        keywords: z.array(z.string()).nullable(),
        start_date: isoDateParser.nullable(),
        end_date: isoDateParser.nullable(),
        url: z.string().url().nullable(),
        // Roles or positions held within the project
        roles: z.array(z.string()).nullable(),
        // Organization or company associated with the project
        entity: z.string().nullable(),
        // Project category (e.g., "Open Source", "Academic", "Commercial")
        type: z.string().nullable(),
      })
    )
    .nullable()
    .default(null),
  meta: z
    .strictObject({
      // URL to the canonical version of this resume
      canonical: z.string().url().nullable(),
      // Schema version or resume format version
      version: z.string().nullable(),
      // When the resume was last updated
      last_modified: z.string().nullable(),
    })
    .nullable()
    .default(null),
});

// Define a role schema for nested positions within a company
// This represents individual positions/roles held at a single company
const roleSchema = z.strictObject({
  // Job title or role
  position: z.string().nullable(),
  start_date: isoDateParser.nullable(),
  // Null/undefined indicates current position
  end_date: isoDateParser.nullable(),
  // Brief description of responsibilities in this role - supports Markdown with template variables
  summary: markdownString.nullable(),
  // Key accomplishments specific to this role
  highlights: z.array(z.string()).nullable(),
  // How the person transitioned into this role
  transition_type: z
    .enum([
      'promotion', // Upward movement within the company
      'lateral', // Same level, different responsibilities
      'department_change', // Moving to a different department
      'initial', // First role at the company
      'demotion', // Downward movement (rare, but possible)
      'restructuring', // Role change due to company reorganization
      'acquisition', // Role change due to company being acquired
      'other', // Any other type of transition
    ])
    .nullable(),
  // Optional additional context about the transition
  transition_note: z.string().nullable(),
});

// Define a parallel role schema for simultaneous responsibilities
// This represents additional roles held concurrently with a primary role
const parallelRoleSchema = roleSchema
  .omit({
    // These fields are inherited from the parent role
    start_date: true,
    end_date: true,
  })
  .extend({
    // Percentage of time allocated to this role (optional)
    time_allocation: z.number().min(0).max(100).nullable(),
  });

// Extended role schema with support for parallel responsibilities
const extendedRoleSchema = roleSchema.extend({
  // Additional parallel roles or responsibilities held simultaneously
  parallel_roles: z.array(parallelRoleSchema).nullable(),
});

// Extended work schema with nested roles for career progression
// This allows modeling promotions and role changes within the same company
const extendedWorkSchema = workSchema.merge(
  z.strictObject({
    // Array of positions/roles held at this company, in chronological or reverse chronological order
    // This allows showing career progression within a company
    positions: z.array(extendedRoleSchema).nullable().default(null),
  })
);

// Extended reference schema with additional contact details
// Enhances the standard reference schema with information needed for contacting references
const extendedReferenceSchema = referenceSchema.extend({
  email: z.string().email().nullable(),
  phone: z.string().nullable(),
  position: z.string().nullable(),
  company: z.string().nullable(),
});

// Extended resume schema with Codarashi-specific enhancements
// This is a superset of the JSON Resume standard that maintains compatibility
// while adding application-specific functionality
export const extendedResume = jsonResume.merge(
  z.strictObject({
    references: z.array(extendedReferenceSchema).nullable().default(null),
    work: z.array(extendedWorkSchema).nullable().default(null),
  })
);

// we have to use arrays, because we want to be able to re-order sections
const editableResume = z
  .strictObject({
    // these items are re-orderable
    header: z.array(
      z.union([
        basicsSchema.shape.name,
        basicsSchema.shape.title,
        basicsSchema.shape.email,
        basicsSchema.shape.phone,
        basicsSchema.shape.url,
        basicsSchema.shape.location,
        basicsSchema.shape.summary,
      ])
    ),
  })
  .merge(
    z.strictObject({
      sections: z.array(
        z.union([
          extendedResume.shape.work,
          extendedResume.shape.volunteer,
          extendedResume.shape.education,
          extendedResume.shape.awards,
          extendedResume.shape.certificates,
          extendedResume.shape.publications,
          extendedResume.shape.skills,
          extendedResume.shape.languages,
          extendedResume.shape.interests,
          extendedResume.shape.references,
          extendedResume.shape.projects,
        ])
      ),
    })
  );

// TODO: ExtendedResume is the underlying Profile - it may contain more info
// EditableResume is the Resume - per job

export type EditableResume = z.infer<typeof editableResume>;

export type Resume = z.infer<typeof extendedResume>;

export const toResume = {
  fromPartial: (data: DeepPartial<Resume>) =>
    zodToDomainOrThrow(extendedResume, data),
};

/**
 * Notes on rich text fields:
 *
 * Fields using markdownString support Markdown formatting with the following features:
 * - Standard Markdown syntax (headings, lists, links, emphasis, etc.)
 * - Template variables using {{ varName }} syntax for dynamic content
 * - Limited HTML tags for additional formatting when needed
 *
 * Example:
 * ```markdown
 * ## Work Experience at {{company}}
 *
 * During my time at {{company}}, I accomplished:
 * - Increased performance by **40%**
 * - Led a team of {{teamSize}} developers
 * - Implemented <span class="highlight">key features</span>
 * ```
 *
 */
