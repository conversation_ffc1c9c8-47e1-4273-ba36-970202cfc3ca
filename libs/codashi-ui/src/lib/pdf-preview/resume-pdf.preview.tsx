import { Document, Page, PDFViewer } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { objectKeys } from '@awe/core';
import { PdfResumeHeader } from './sections/header';

type Props = {
  profile: Resume;
  height?: number | string;
};

export const ResumePdfPreview: FC<Props> = ({ profile, height = '100%' }) => {
  return (
    <div style={{ width: '100%', height }}>
      <PDFViewer style={{ width: '100%', height }}>
        <Document>
          <Page>
            {objectKeys(profile).map((key) => {
              return (
                key === 'basics' &&
                profile[key] && (
                  <PdfResumeHeader section={profile[key]} key={key} />
                )
              );
            })}
          </Page>
        </Document>
      </PDFViewer>
    </div>
  );
};
