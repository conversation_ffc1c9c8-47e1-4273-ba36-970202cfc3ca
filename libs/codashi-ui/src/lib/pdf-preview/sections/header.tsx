import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { exhaustive } from 'exhaustive';
import React from 'react';

import { type Resume } from '@awe/codashi-core';
import { objectKeys } from '@awe/core';

type Props = {
  section: NonNullable<Resume['basics']>;
};

export const PdfResumeHeader: React.FC<Props> = ({ section }) => {
  return (
    <View style={styles.container}>
      {objectKeys(section).map((key) => {
        return exhaustive(key, {
          name: () => <Text style={styles.name}>{section.name}</Text>,
          title: () => <Text style={styles.item}>{section.title}</Text>,
          email: () => <Text style={styles.email}>{section.email}</Text>,
          phone: () => <Text style={styles.item}>{section.phone}</Text>,
          url: () => <Text style={styles.item}>{section.url}</Text>,
          image: () => null,
          location: () => {
            const { location } = section;

            if (!location) {
              return null;
            }

            return (
              <Text style={styles.item}>
                {[
                  location.address,
                  location.city,
                  location.region,
                  location.country_code,
                  location.postal_code,
                ]
                  .filter(Boolean)
                  .join(', ')}
              </Text>
            );
          },
          profiles: () => {
            const { profiles } = section;
            if (!profiles || profiles.length === 0) {
              return null;
            }

            return (
              <View style={styles.profiles}>
                {profiles.map((profile, idx) => (
                  <Text style={styles.item} key={idx}>
                    {profile.network ? `${profile.network}: ` : ''}
                    {profile.username || ''}
                    {profile.url ? ` (${profile.url})` : ''}
                  </Text>
                ))}
              </View>
            );
          },
          summary: () => <Text style={styles.item}>{section.summary}</Text>,
        });
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    flexWrap: 'wrap',
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  item: {
    marginBottom: 6,
    fontSize: 12,
  },
  email: {
    color: 'blue',
    marginBottom: 6,
    fontSize: 12,
  },
  profiles: {
    marginTop: 8,
  },
});
