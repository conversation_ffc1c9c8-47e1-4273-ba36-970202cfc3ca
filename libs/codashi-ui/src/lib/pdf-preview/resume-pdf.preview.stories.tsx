import type { Meta, StoryObj } from '@storybook/react';
import { exampleProfile } from './data';
import { ResumePdfPreview } from './resume-pdf.preview';

const meta: Meta<typeof ResumePdfPreview> = {
  component: ResumePdfPreview,
  title: 'PDF/ResumePdfPreview',
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof ResumePdfPreview>;

export const Default: Story = {
  args: {
    profile: exampleProfile,
    height: '600px',
  },
};
